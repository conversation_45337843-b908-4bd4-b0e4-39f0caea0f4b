#!/bin/bash

# Lemomate 后端房间名修复更新脚本
echo "开始更新 Lemomate 后端以修复中文房间名问题..."

# 错误处理
set -e

# 检查是否以root运行
if [ "$(id -u)" -ne 0 ]; then
    echo "错误：请以root用户运行此脚本（使用sudo）"
    exit 1
fi

# 进入项目目录
PROJECT_DIR="/path/to/lemomate_au"  # 请替换为实际的项目路径
cd $PROJECT_DIR

echo "当前目录: $(pwd)"

# 拉取最新代码（如果使用Git）
echo "拉取最新代码..."
git pull

# 构建后端
echo "构建后端应用..."
mvn clean package -DskipTests

# 备份当前JAR文件
echo "备份当前JAR文件..."
cp /home/<USER>/lemomate.jar /home/<USER>/lemomate.jar.bak.$(date +%Y%m%d%H%M%S)

# 停止服务
echo "停止Lemomate服务..."
systemctl stop lemomate

# 复制新的JAR文件
echo "部署新的JAR文件..."
cp target/lemomate-0.0.1-SNAPSHOT.jar /home/<USER>/lemomate.jar

# 设置权限
echo "设置文件权限..."
chmod 755 /home/<USER>/lemomate.jar

# 启动服务
echo "启动Lemomate服务..."
systemctl start lemomate

# 等待服务启动
echo "等待服务启动..."
sleep 10

# 检查服务状态
echo "检查Lemomate服务状态..."
if systemctl is-active --quiet lemomate; then
    echo "✅ 更新成功！Lemomate服务正在运行"
    echo "房间名修复已应用，现在支持中文会议标题"
else
    echo "❌ 服务启动失败，请检查日志"
    echo "查看日志命令: journalctl -u lemomate -f"
    exit 1
fi

echo "更新完成！"
echo ""
echo "修复内容："
echo "1. 修复了中文会议标题导致的房间名不匹配问题"
echo "2. 添加了中文转拼音功能"
echo "3. 确保房间名只包含ASCII字符"
echo ""
echo "现在可以使用中文会议标题创建会议了！"
