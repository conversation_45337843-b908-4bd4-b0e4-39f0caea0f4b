#!/bin/bash

# Lemomate 团队管理功能更新脚本
echo "开始更新 Lemomate 团队管理功能..."

# 错误处理
set -e

# 检查是否以root运行
if [ "$(id -u)" -ne 0 ]; then
    echo "错误：请以root用户运行此脚本（使用sudo）"
    exit 1
fi

# 进入项目目录
PROJECT_DIR="/path/to/lemomate_au"  # 请替换为实际的项目路径
cd $PROJECT_DIR

echo "当前目录: $(pwd)"

# 拉取最新代码（如果使用Git）
echo "拉取最新代码..."
git pull

# 构建前端
echo "构建前端..."
cd frontend
npm run build
cd ..

# 构建后端
echo "构建后端应用..."
mvn clean package -DskipTests -U

# 备份当前JAR文件
echo "备份当前JAR文件..."
cp /home/<USER>/lemomate.jar /home/<USER>/lemomate.jar.bak.$(date +%Y%m%d%H%M%S)

# 停止服务
echo "停止Lemomate服务..."
systemctl stop lemomate

# 复制新的JAR文件
echo "部署新的JAR文件..."
cp target/lemomate-0.0.1-SNAPSHOT.jar /home/<USER>/lemomate.jar

# 复制前端文件
echo "部署前端文件..."
rm -rf /var/www/lemomate/*
cp -r frontend/dist/* /var/www/lemomate/

# 设置权限
echo "设置文件权限..."
chmod 755 /home/<USER>/lemomate.jar
chown -R www-data:www-data /var/www/lemomate

# 启动服务
echo "启动Lemomate服务..."
systemctl start lemomate

# 等待服务启动
echo "等待服务启动..."
sleep 15

# 检查服务状态
echo "检查Lemomate服务状态..."
if systemctl is-active --quiet lemomate; then
    echo "✅ 更新成功！Lemomate服务正在运行"
    echo ""
    echo "新增功能："
    echo "1. 团队管理员可以在团队成员页面删除成员"
    echo "2. 平台管理员可以在团队管理页面删除团队"
    echo "3. 创建团队时可以设置最大成员数量（默认50人，可设置1-1000人）"
    echo "4. 团队列表显示当前成员数/最大成员数"
    echo ""
    echo "注意事项："
    echo "- 只有空团队（无成员）才能被删除"
    echo "- 团队管理员不能移除自己，需要联系平台管理员"
    echo "- 平台管理员拥有所有权限"
else
    echo "❌ 服务启动失败，请检查日志"
    echo "查看日志命令: journalctl -u lemomate -f"
    exit 1
fi

echo "更新完成！"
